.wrapperContent {
  // width: 100%;
  // max-height: 100vh;
  border-radius: 4px;
  background: #fff;
  // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px;

  .channelTypeWrapper {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;

    .channelTypeItem {
      width: 140px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      border: 1px solid #D4D6D9;
      cursor: pointer;
      transition: all 0.3s ease;
      user-select: none;

      &:hover {
        border: 1px solid #3463FC;
        background: rgba(52, 99, 252, 0.02);
      }

      &.active {
        border: 1px solid #3463FC;
        background: rgba(52, 99, 252, 0.05);
      }

      :global {
        .ant-radio-wrapper {
          margin: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          pointer-events: none; // 禁用radio本身的点击事件，由外层div处理

          .ant-radio {
            top: 0;
            pointer-events: none;
          }

          span:last-child {
            pointer-events: none;
          }
        }
      }
    }
  }

  :global {

    .ant-radio-wrapper,
    .ant-form label {
      font-size: 12px;
    }

    .ant-form-item .ant-form-item-label {
      font-weight: normal;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      color: '#3463FC' !important;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }

    .ant-select {
      width: 23%;
      float: left;
      background: #ffffff;
      margin-right: 20px;
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      background: #ffffff;
      border-radius: 6px 6px 6px 6px;
      box-shadow: none;
    }

    .ant-btn .anticon {
      margin-right: 4px;
    }

    .ant-select-selection-item img {
      width: 16px;
      height: 16px;
      margin-right: 3px;
      margin-top: -2px;
    }
  }

  .selectWrapper {
    padding: 10px;
    border-radius: 10px;
    background: #f5f3fe;
    margin-bottom: 20px;

    p {
      color: #333;
      font-size: 14px;
      font-weight: 700;
    }
  }

  .rulesBox {
    padding: 10px;
    border-radius: 4px;
    background: #f9f9f9;
    margin-bottom: 20px;
  }

  .personalityBox {
    .customerTagContainer {
      :global {
        .ant-select {
          background: #fff;

          .ant-tag {
            height: 24px;
            font-size: 12px;
            padding: 1px 10px;
            margin-bottom: 2px;
            display: flex;

            span {
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }

            color: #3463fc;
            border-radius: 4px;
            border: 1px solid #3463fc;
            background: #fffbf4;

            .ant-tag-close-icon {
              color: #3463fc;
            }
          }

          .ant-tag:first-child {
            margin-left: 0px;
          }

          .colorType1 {
            color: #3463fc;
            border-radius: 4px;
            border: 1px solid #3463fc;
            background: rgba(52, 99, 252, 0.1);

            span,
            .ant-tag-close-icon {
              color: #3463fc;
            }
          }

          .colorType2 {
            color: #d7ce1e;
            border-radius: 4px;
            border: 1px solid #d7ce1e;
            background: rgba(0, 185, 0, 0.1);

            span,
            .ant-tag-close-icon {
              color: #d7ce1e;
            }
          }

          .colorType3 {
            color: #ad30e5;
            border-radius: 4px;
            border: 1px solid #ad30e5;
            background: rgba(173, 48, 229, 0.1);

            span,
            .ant-tag-close-icon {
              color: #ad30e5;
            }
          }

          .colorType4 {
            color: #2d6973;
            border-radius: 4px;
            border: 1px solid #2d6973;
            background: rgba(45, 105, 115, 0.1);

            span,
            .ant-tag-close-icon {
              color: #2d6973;
            }
          }

          .colorType5 {
            color: #f22417;
            border-radius: 4px;
            border: 1px solid #f22417;
            background: rgba(242, 36, 23, 0.1);

            span,
            .ant-tag-close-icon {
              color: #f22417;
            }
          }

          .colorType6 {
            color: #d7ce1e;
            border-radius: 4px;
            border: 1px solid #d7ce1e;
            background: rgba(215, 206, 30, 0.1);

            span,
            .ant-tag-close-icon {
              color: #d7ce1e;
            }
          }

          .colorType7 {
            color: #86bf00;
            border-radius: 4px;
            border: 1px solid #86bf00;
            background: rgba(134, 191, 0, 0.1);

            span,
            .ant-tag-close-icon {
              color: #86bf00;
            }
          }

          .colorType8 {
            color: #fb8f83;
            border-radius: 4px;
            border: 1px solid #fb8f83;
            background: rgba(251, 143, 131, 0.1);

            span,
            .ant-tag-close-icon {
              color: #fb8f83;
            }
          }

          .colorType9 {
            color: #64635e;
            border-radius: 4px;
            border: 1px solid #64635e;
            background: rgba(100, 99, 94, 0.1);

            span,
            .ant-tag-close-icon {
              color: #64635e;
            }
          }

          .colorType10 {
            color: #9c9992;
            border-radius: 4px;
            border: 1px solid #9c9992;
            background: rgba(156, 153, 146, 0.1);

            span,
            .ant-tag-close-icon {
              color: #9c9992;
            }
          }

          .colorType11 {
            color: #feda92;
            border-radius: 4px;
            border: 1px solid #feda92;
            background: #fffbf4;
          }
        }

        .ant-input {
          width: 25%;
          float: left;
          border-radius: 6px;
          margin-right: 2%;
          color: #3463fc;
        }
      }
    }

    .personalitySty {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      :global {
        .ant-input-affix-wrapper>.ant-input {
          color: #3463fc !important;
        }
      }
    }
  }

  .tootip {
    color: #999;
    font-size: 12px;
    margin-bottom: 10px;
    margin-top: -15px;
  }

  .delOutSide {
    float: right;
    display: flex;
    padding: 6px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    height: 28px;
    line-height: 28px;
    background: #f22417;

    span {
      display: inline-block;
    }
  }

  .footerBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 28px;

    .saveHover:hover {
      background-color: #4873fc;
    }

    .cancelHover:hover {
      background-color: #f5f5f5;
    }
  }
}

.defaultStyle {
  //width: 100%;
  // max-height: 100vh;
  border-radius: 4px;
  // background: #fff;
  // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  margin: 20px;
  // padding: 20px;

  :global {
    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      color: '#3463FC' !important;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }

    .ant-select {
      width: 23%;
      float: left;
      background: #ffffff;
      margin-right: 20px;
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      background: #ffffff;
      border-radius: 6px 6px 6px 6px;
      box-shadow: none;
    }

    .ant-btn .anticon {
      margin-right: 4px;
    }

    .ant-select-selection-item img {
      width: 16px;
      height: 16px;
      margin-right: 3px;
      margin-top: -2px;
    }
  }

  .selectWrapper {
    padding: 10px;
    border-radius: 10px;
    background: #f5f3fe;
    margin-bottom: 20px;

    p {
      color: #333;
      font-size: 14px;
      font-weight: 700;
    }

    .defaultTitle {
      color: #333;
      font-size: 14px;
      font-weight: 700;
    }

    .defaultTooip {
      color: #999;
      font-size: 12px;
      font-weight: 400;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  .defaultFooterBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;

    .saveHover:hover {
      background-color: #4873fc;
    }

    .cancelHover:hover {
      background-color: #f5f5f5;
    }
  }
}
