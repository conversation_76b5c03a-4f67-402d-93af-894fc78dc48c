import React, { useState, useEffect, useRef } from 'react';
import styles from './index.less';
import {
  Select,
  Form,
  Spin,
  notification,
  Button,
  Row,
  Col,
  Radio,
  Checkbox,
  Popconfirm,
  Tag,
  Input,
} from 'antd';
const { Option, OptGroup } = Select;
import { useDispatch, getIntl, FormattedMessage, history } from 'umi';
import { Delete, AddRule, Add, DeleteRed } from '../icon';
import { generateRandomNumber } from '@/utils/utils';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件

import { isArray, set } from 'lodash';

const AllocationRuleAdd = () => {
  const dispatch = useDispatch();
  const formChannelType = useRef(null);
  const defaultForm = useRef(null);
  const formRule = useRef(null);
  const [channelTypeList, setChannelTypeList] = useState([]); // 渠道类型
  const [channelType, setChannelType] = useState('0'); // 渠道类型
  const [workRecordTypeList, setWorkRecordTypeList] = useState([]); //工单类型
  const [originWorkRecordTypeList, setOriginWorkRecordTypeList] = useState([]); //原始工单类型
  let [channelOptions, setChannelOptions] = useState([]); // 个性化规则下
  let [originChannelOptions, setOriginChannelOptions] = useState([]); // 原始渠道名称
  let [channelId, setChannelId] = useState([]); // 渠道类型下来源渠道
  const [callTeamList, setCallTeamList] = useState([]); // 团队
  const [callAgentList, setAgentList] = useState([]); // 座席
  let [ruleList, setRuleList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [standardTagList, setStandardTasList] = useState([]); // 客户标签
  const [standardTagListRender, setStandardTasListRender] = useState([]); // 客户标签输入框渲染
  const [orginStandardTagList, setOriginStandardTasList] = useState([]); // 原始客户标签
  let [res, setRes] = useState({});
  let [languageList, setLanguageList] = useState([]); //  客户语言
  let [gradeList, setGradeList] = useState([]); //  客户等级
  let [customerCountry, setCustomerCountry] = useState([]); //  客户国家
  let [loadingBtn, setLoadingBtn] = useState(false); //  按钮保存
  let [determineTypeValue, setDetermineTypeValue] = useState(1); //  判断类型

  // 个性化配置新增规则
  let [uniquesList, setUniquesList] = useState([
    {
      conditionType: '',
      conditionValues: [],
    },
  ]);
  // 判断规则--任意条件
  let [routingRule, setRoutingRule] = useState([
    {
      onlyId: generateRandomNumber(6),
      judgmentRuleType: determineTypeValue, // 1:任意条件；2:个性化配置；3:默认规则
      distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定坐席
      distributionIds: [], // 分配的目标id，团队就写团队id，坐席就写坐席
      distributionIdsNameList: [], //坐席团队名字
      stickiness: 1, // 粘性分配 0否 1是
      uniquesList: uniquesList,
    },
  ]);

  const [threeChannelList, setThreeChannelList] = useState([
    {
      label: '电话',
      value: 1,
    },
    {
      label: '聊天',
      value: 2,
    },
    {
      label: '邮件',
      value: 3,
    }
  ]);
  const [currentThreeChannel, setCurrentThreeChannel] = useState(1);
  const handleChangeThreeChannel = (value, index) => {
    setCurrentThreeChannel(value);
  };
  useEffect(() => {
    const fetchData = async () => {
      await queryChannelTypeList();
      await queryAllStandardTag();
      await queryRuleList();
      await queryWorkRecordType();
      await queryTeamList();
      await querySeatsUser();
      await getLanguageList();
      await gradeListQuery();
      await queryCountryDef();
    };
    fetchData();
    if (formChannelType.current) {
      formChannelType.current?.setFieldsValue({
        channelType: '0',
      });
    }
  }, []);
  useEffect(() => {
    let storage = JSON.parse(localStorage.getItem('editObj'));
    const fetch = async () => {
      await queryAllStandardTag();
      if (history.location.state?.routingId || storage.routingId) {
        await handleEdit(
          history.location.state?.routingId || storage.routingId,
        );
      }
    };
    fetch();
  }, [history.location.state?.routingId]);
  useEffect(() => {
    let storage = JSON.parse(localStorage.getItem('editObj'));
    if (history.location.state?.type == 'add' || storage.type == 'add') {
      if (formChannelType.current) {
        formChannelType.current?.setFieldsValue({
          channelType: '0',
        });
        getChannels(channelType);
      }
    }
  }, [history.location.state?.type]);

  useEffect(() => {
    if (formChannelType.current && Object.keys(res).length > 0) {
      formChannelType.current?.setFieldsValue({
        channelType: res.channelType + '',
        channelId: channelId,
      });
      getChannels(res.channelType);
    }
    if (formRule.current && res.routingRule) {
      const initialData = res.routingRule.reduce((acc, rule, index) => {
        acc[`routingRule[${index}].distributionIds`] =
          rule.distributionIds && rule.distributionIds.split(',');
        return acc;
      }, {});
      formRule.current.setFieldsValue(initialData);
    }
    // 默认规则回显
    if (defaultForm.current && res.routingRule) {
      const initialData = res.routingRule.reduce((acc, rule, index) => {
        acc[`routingRule[${index}].distributionIds`] =
          rule.distributionIds && rule.distributionIds.split(',');
        return acc;
      }, {});
      defaultForm.current.setFieldsValue(initialData);
    }
  }, [res]);

  // 处理字符串id变成数组id
  const handleidFomartArray = rules => {
    return (
      rules &&
      rules.map(rule => ({
        routingRuleId: rule.routingRuleId,
        judgmentRuleType: rule.judgmentRuleType,
        distributionRuleType: rule.distributionRuleType,
        stickiness: rule.stickiness,
        distributionIdsNameList: rule.distributionIdsNameList,
        distributionIds: rule.distributionIds
          ? rule.distributionIds.split(',').map(id => id.trim())
          : [],
        uniquesList: rule.uniquesList
          ? rule.uniquesList.map(item => ({
            ...item,
            conditionValues: formatConditionValues(item),
          }))
          : [],
      }))
    );
  };
  // 处理回显时电话和邮件默认展示字符串
  const formatConditionValues = item => {
    if (
      item.conditionType === 'customer_email' ||
      item.conditionType === 'customer_phone'
    ) {
      if (item.conditionValues && item.conditionValues.length > 0) {
        return item.conditionValues;
      }
      return '';
    } else {
      if (
        typeof item.conditionValues === 'string' &&
        item.conditionValues.length > 0
      ) {
        return item.conditionValues.split(',').map(val => val.trim());
      }
      return [];
    }
  };
  // 修改回显
  const handleEdit = routingId => {
    setLoading(true);
    dispatch({
      type: 'allocation/editRuleInfo',
      payload: { routingId: routingId },
      callback: response => {
        if (response.code == 200) {
          let { routingChannel } = response.data;
          if (history.location.state?.defaultRouting === 1) {
            setRoutingRule(prevRoutingRule => {
              const newRoutingRule = handleidFomartArray(
                response.data.routingRule,
              );
              return newRoutingRule;
            });
            setRes(response.data);
          }
          let channelIds = routingChannel.map(item => item.channelId);
          setDetermineTypeValue(response.data.judgmentRuleType);
          setChannelId(channelIds);
          setChannelType(response.data.channelType + '');
          setRoutingRule(prevRoutingRule => {
            const newRoutingRule = handleidFomartArray(
              response.data.routingRule,
            );
            // console.log(newRoutingRule, 'newRoutingRule');
            return newRoutingRule;
          });
          setTimeout(() => {
            formRule.current?.setFieldsValue({
              routingRule: handleidFomartArray(response.data.routingRule),
            });
          }, 0);
          setRes(response.data);
          setLoading(false);
        }
      },
    });
  };
  // 查询客户等级接口
  const gradeListQuery = () => {
    dispatch({
      type: 'customerDataGroupManagement/gradeList',
      callback: response => {
        if (response.code == 200) {
          setGradeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户国家
  const queryCountryDef = () => {
    dispatch({
      type: 'allocation/queryCustomerCountryList',
      callback: response => {
        if (response.code == 200) {
          setCustomerCountry(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户标签
  const queryAllStandardTag = () => {
    dispatch({
      type: 'allocation/queryAllStandardTag',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            setStandardTasList(response.data);
            setStandardTasListRender(response.data);
            setOriginStandardTasList(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询路由规则
  const queryRuleList = () => {
    dispatch({
      type: 'workOrderCenter/queryRuleList',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            const filteredResponse = response.data?.map(group => ({
              ...group,
              routingRuleList: group.routingRuleList.filter(
                rule => rule.code !== 'customer_channel_source',
              ),
            }));
            setRuleList(filteredResponse);
            // console.log(filteredResponse, 'response.data');
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询座席
  const querySeatsUser = () => {
    dispatch({
      type: 'workOrderCenter/querySeatsUser',
      callback: response => {
        if (response.code == 200) {
          setAgentList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询团队列表
  const queryTeamList = () => {
    dispatch({
      type: 'workOrderCenter/queryCallDeptList',
      callback: response => {
        if (response.code == 200) {
          setCallTeamList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询客户语言
  const getLanguageList = () => {
    // dispatch({
    //   type: 'personalCenter/listLanguage',
    //   callback: response => {
    //     let { code, data, msg } = response;
    //     if (code === 200) {
    //       setLanguageList(data);
    //     } else {
    //       notification.error({
    //         message: msg,
    //       });
    //     }
    //   },
    // });
    // 翻译的语言列表
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    setLanguageList(googleLanguage);
  };

  // 请选择渠道类型下切换来源渠道
  const handleChangeSelectChannel = value => {
    setChannelId(value);
  };
  // 根据渠道类型查来源渠道
  const getChannels = value => {
    const params = {
      pageSize: 1000,
      pageNum: 1,
      channelTypeId: value,
    };
    if (params.channelTypeId == 0) {
      delete params.channelTypeId;
    }
    dispatch({
      type: 'channel/getChannel',
      payload: params,
      callback: res => {
        if (res.code === 200) {
          let result = res.data.rows;
          setChannelOptions(result);
          setOriginChannelOptions(result);
        }
      },
    });
  };

  // 个性化规则下

  // 切换渠道类型
  const handleChangeSelect = value => {
    if (value !== undefined) {
      getChannels(value);
      setChannelType(value);
      setChannelId([]);
    }
  };

  // 查询工单类型列表
  const queryWorkRecordType = () => {
    dispatch({
      type: 'workOrderCenter/queryWorkRecordType',
      callback: response => {
        if (response.code == 200) {
          let workRecordTypeList = response.data;
          setWorkRecordTypeList(workRecordTypeList);
          setOriginWorkRecordTypeList(workRecordTypeList);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询渠道类型列表
  const queryChannelTypeList = () => {
    dispatch({
      type: 'newChannelConfiguration/queryNewChannelTypeList',
      callback: response => {
        if (response.code == 200) {
          const newData = [
            {
              code: '0',
              name: getIntl().formatMessage({
                id: 'marketing.channel.type.all.small',
                defaultMessage: '所有渠道',
              }),
            },
            ...response.data,
          ];
          setChannelTypeList(newData);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换任意条件和个性化选择
  const onChangeCheck = e => {
    setDetermineTypeValue(e.target.value);
    // 生成6为随机ID
    const num = generateRandomNumber(6);
    if (e.target.value === 2) {
      setRoutingRule([
        {
          onlyId: num,
          judgmentRuleType: e.target.value, // 1:任意条件；2:个性化配置；3:默认规则
          distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定坐席
          distributionIds: [], // 分配的目标id，团队就写团队id，坐席就写坐席
          distributionIdsNameList: [], //坐席团队名字
          stickiness: 1, // 粘性分配 0否 1是
          uniquesList: [
            {
              conditionType: '',
              conditionValues: [],
            },
          ],
        },
      ]);
      formRule.current?.resetFields([`routingRule[0].distributionIds`]);
      formRule.current?.setFieldsValue({
        routingRule: [
          {
            onlyId: num,
            judgmentRuleType: e.target.value, // 1:任意条件；2:个性化配置；3:默认规则
            distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定坐席
            distributionIds: [], // 分配的目标id，团队就写团队id，坐席就写坐席
            distributionIdsNameList: [], //坐席团队名字
            stickiness: 1, // 粘性分配 0否 1是
            uniquesList: [
              {
                conditionType: '',
                conditionValues: [],
              },
            ],
          },
        ],
      });
    } else {
      setRoutingRule([
        {
          onlyId: num,
          judgmentRuleType: e.target.value, // 1:任意条件；2:个性化配置；3:默认规则
          distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定坐席
          distributionIds: [], // 分配的目标id，团队就写团队id，坐席就写坐席
          distributionIdsNameList: [], //坐席团队名字
          stickiness: 1, // 粘性分配 0否 1是
          uniquesList: [],
        },
      ]);
      formRule.current?.resetFields([`routingRule[0].distributionIds`]);
      formRule.current?.setFieldsValue({
        routingRule: [
          {
            onlyId: num,
            judgmentRuleType: e.target.value, // 1:任意条件；2:个性化配置；3:默认规则
            distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定坐席
            distributionIds: [], // 分配的目标id，团队就写团队id，坐席就写坐席
            distributionIdsNameList: [], //坐席团队名字
            stickiness: 1, // 粘性分配 0否 1是
            uniquesList: [],
          },
        ],
      });
    }
  };

  //个性化配置切换路由对应规则id
  const hanldeChangeChannalCode = async (type, value, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = value;
    // 把工单名字和渠道来源名字保存
    if (type === 'workRecordType') {
      const filteredOptions = originWorkRecordTypeList.filter(
        o => !value.includes(o.workRecordTypeName),
      );
      setWorkRecordTypeList(filteredOptions);
      newRoutingRuleList[ruleIndex].uniquesList[index].conditionValuesNameList =
        newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues;
    } else if (type === 'channelId') {
      const filteredOptions = originChannelOptions.filter(
        o => !value.includes(o.name),
      );
      setChannelOptions(filteredOptions);
      newRoutingRuleList[ruleIndex].uniquesList[
        index
      ].conditionValuesNameList = await handleNameChnnelId(
        newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues,
        originChannelOptions,
      );
    }
    setRoutingRule(newRoutingRuleList);
  };

  // 选择客户语言
  const hanldeChangeLanguageCode = async (value, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = value;
    newRoutingRuleList[ruleIndex].uniquesList[
      index
    ].conditionValuesNameList = await handleLanguageName(
      newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues,
      languageList,
    );
    setRoutingRule(newRoutingRuleList);
  };

  // 选择客户等级
  const hanldeChangeCustomerlevelCode = async (value, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = value;
    newRoutingRuleList[ruleIndex].uniquesList[
      index
    ].conditionValuesNameList = await handleCustomerlevelName(
      newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues,
      gradeList,
    );
    setRoutingRule(newRoutingRuleList);
  };

  // 处理客户国家
  const hanldeChangeCustomerCountryCode = async (value, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = value;
    newRoutingRuleList[ruleIndex].uniquesList[
      index
    ].conditionValuesNameList = await handleCustomerCountryName(
      newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues,
      customerCountry,
    );
    // console.log(newRoutingRuleList, 'newRoutingRuleList');
    setRoutingRule(newRoutingRuleList);
  };
  // 输入客户邮箱
  const hanldeChangeCustomerEmailCode = (e, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues =
      e.target.value;
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValuesNameList = [
      e.target.value,
    ];
    setRoutingRule(newRoutingRuleList);
  };
  // 输入客户电话 饿
  const hanldeChangeCustomerPhoneCode = (e, index, ruleIndex) => {
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues =
      e.target.value;
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValuesNameList = [
      e.target.value,
    ];
    setRoutingRule(newRoutingRuleList);
  };
  // 处理客户国家名字
  const handleCustomerCountryName = (conditionValues, dataList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        dataList && dataList.find(item => item.countryCode == conditionValue);
      if (matchedDept) {
        matchedDepts.push(matchedDept.countryName);
      }
    });
    return matchedDepts;
  };

  // 处理客户等级名字
  const handleCustomerlevelName = (conditionValues, dataList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        dataList && dataList.find(item => item.gradeId == conditionValue);
      if (matchedDept) {
        matchedDepts.push(matchedDept.gradeName);
      }
    });
    return matchedDepts;
  };
  // 处理客户语言名字
  const handleLanguageName = (conditionValues, dataList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        dataList && dataList.find(item => item.label == conditionValue);
      if (matchedDept) {
        matchedDepts.push(matchedDept.label);
      }
    });
    return matchedDepts;
  };

  // 处理渠道来源名字
  const handleNameChnnelId = (conditionValues, dataList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        dataList && dataList.find(item => item.channelId == conditionValue);
      if (matchedDept) {
        matchedDepts.push(matchedDept.name);
      }
    });
    return matchedDepts;
  };
  // 处理客户标签名字
  const handleTagsCustomerName = (conditionValues, standardTagList) => {
    if (!conditionValues) return null;
    if (!standardTagList) return null;
    // const tagList = standardTagList.flatMap(item => item.tagList);
    const tagList = orginStandardTagList.flatMap(item => item.tagList);
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        tagList && tagList.find(item => item.tagId == conditionValue);
      if (matchedDept) {
        matchedDepts.push(matchedDept.tagContent);
      }
    });
    return matchedDepts;
  };
  // 选择客户标签
  const hanldeChangeCustomerTag = async (selectedValue, index, ruleIndex) => {
    if (selectedValue) {
      const newRoutingRuleList = [...routingRule]; // 复制当前的状态
      let conditionValues =
        newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues || [];
      if (!Array.isArray(conditionValues)) {
        conditionValues = [conditionValues];
      }
      conditionValues.push(selectedValue);
      newRoutingRuleList[ruleIndex].uniquesList[
        index
      ].conditionValues = conditionValues;
      const filterOption = orginStandardTagList.map(category => ({
        ...category,
        tagList: category.tagList.filter(
          tag => !conditionValues.includes(tag.tagId),
        ),
      }));
      setStandardTasList(filterOption);
      // 把客户标签名字保存
      newRoutingRuleList[ruleIndex].uniquesList[
        index
      ].conditionValuesNameList = await handleTagsCustomerName(
        newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues,
        standardTagList,
      );
      setRoutingRule(newRoutingRuleList);
    }
  };
  // s删除客户标签
  const handleDeselect = (selectedValue, index, ruleIndex) => {
    if (selectedValue) {
      const newRoutingRuleList = [...routingRule]; // 复制当前的状态
      let conditionValues =
        newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues || [];
      if (!Array.isArray(conditionValues)) {
        conditionValues = [conditionValues];
      }
      let findIndex = conditionValues.findIndex(item => item == selectedValue);
      if (findIndex > -1) {
        conditionValues.splice(findIndex, 1);
      }
      const filterOption = orginStandardTagList.map(category => ({
        ...category,
        tagList: category.tagList.filter(
          tag => !conditionValues.includes(tag.tagId),
        ),
      }));
      setStandardTasList(filterOption); // 删除时回复下拉
      newRoutingRuleList[ruleIndex].uniquesList[
        index
      ].conditionValues = conditionValues;
      setRoutingRule(newRoutingRuleList);
    }
  };
  // 清除所有选项
  const handleClearAllOption = (selectedValue, index, ruleIndex) => {
    // console.log(selectedValue, index, ruleIndex);
    const newRoutingRuleList = [...routingRule]; // 复制当前的状态
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = [];
    setRoutingRule(newRoutingRuleList);
  };
  // 选中后tag标签颜色
  const tagRender = props => {
    const { label, value, closable, onClose } = props;
    let resultObj = findTagById(value);
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    if (resultObj) {
      return (
        <Tag
          className={resultObj.tagColorCode || 'colorType1'}
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 3,
          }}
        >
          {resultObj.tagContent}
        </Tag>
      );
    }
  };
  const findTagById = tagId => {
    for (let category of standardTagListRender) {
      for (let tag of category.tagList) {
        if (tag.tagId == tagId) {
          return tag;
        }
      }
    }
    return null; // 如果没有找到匹配的标签，返回 null
  };

  //切换路由规则，'ticket_type':工单类型，'channel_name':渠道名称，'customer_label':客户标签
  const handleChangePersonaltyRule = (value, ruleIndex, index) => {
    // console.log(value, ruleIndex, index, '=====xxxx===');
    const newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionType = value;
    newRoutingRuleList[ruleIndex].uniquesList[index].conditionValues = []; // 清空规则id取值
    setRoutingRule(newRoutingRuleList);
  };
  // 添加个性话配置
  const handleAddRoutingRule = ruleIndex => {
    let newObj = {
      conditionType: '',
      conditionValues: [],
    };
    let newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList.push(newObj);
    setRoutingRule(newRoutingRuleList);
  };
  // 删除个性化配置下
  const handleDelUniqueRule = (index, ruleIndex) => {
    let newRoutingRuleList = [...routingRule];
    newRoutingRuleList[ruleIndex].uniquesList.splice(index, 1);
    setRoutingRule(newRoutingRuleList);
  };

  // 分配给团队或者座席
  const onChangeDistributionRuleType = (e, index, type) => {
    let newRoutingRule = [...routingRule];
    newRoutingRule[index].distributionRuleType = e.target.value;
    newRoutingRule[index].distributionIds = [];
    newRoutingRule[index].distributionIdsNameList = [];
    setRoutingRule([...newRoutingRule]);
    if (type == 'defaultForm') {
      defaultForm.current?.setFieldsValue([
        `routingRule[${index}].distributionIds`,
      ]);
    } else {
      formRule.current?.resetFields([`routingRule[${index}].distributionIds`]);
      formRule.current?.setFieldsValue({ routingRule: [...newRoutingRule] });
    }
  };
  // 选择团队或者座席
  const handleChoseTeamOrAgent = async (type, value, ruleIndex) => {
    let newRoutingRule = [...routingRule];
    newRoutingRule[ruleIndex].distributionIds = value;
    // 把座席、团队名字传过去
    if (type === 'agent') {
      newRoutingRule[
        ruleIndex
      ].distributionIdsNameList = await filterNameByAgent(
        newRoutingRule[ruleIndex].distributionIds,
        callAgentList,
      );
    } else {
      newRoutingRule[
        ruleIndex
      ].distributionIdsNameList = await filterNameByAgentOrTeam(
        newRoutingRule[ruleIndex].distributionIds,
        callTeamList,
      );
    }
    setRoutingRule(newRoutingRule);
  };

  // 过滤团队名字
  const filterNameByAgentOrTeam = (distributionIds, dataList) => {
    if (!distributionIds) return null;
    const matchedDepts = [];
    distributionIds.forEach(distributionId => {
      const matchedDept =
        dataList && dataList.find(dept => dept.deptId === distributionId);
      if (matchedDept) {
        matchedDepts.push(matchedDept.deptName);
      }
    });
    return matchedDepts;
  };
  // 过滤座席名字
  const filterNameByAgent = (distributionIds, dataList) => {
    if (!distributionIds) return null;
    const matchedDepts = [];
    distributionIds.forEach(distributionId => {
      const matchedDept =
        dataList && dataList.find(dept => dept.userId === distributionId);
      if (matchedDept) {
        matchedDepts.push(matchedDept.userName);
      }
    });
    return matchedDepts;
  };
  // 设置粘性分配
  const handleSetSticky = (e, index) => {
    let stickiness = 1 ? e.target.checked : 0;
    let newRoutingRule = [...routingRule];
    newRoutingRule[index].stickiness = stickiness;
    setRoutingRule(newRoutingRule);
  };

  const handleFormateChandle = () => {
    const matchedChannels = channelOptions
      .filter(channel => channelId.includes(channel.channelId))
      .map(channel => ({
        channelId: channel.channelId,
        routingChannelName: channel.name,
      }));
    return matchedChannels;
  };

  // 处理数组id变成字符串id
  const handleParamsId = rules => {
    return (
      rules &&
      rules.map(rule => ({
        ...rule,
        distributionIds: rule.distributionIds && rule.distributionIds.join(','),
        // distributionIdsNameList: filterNameByAgentOrTeam(
        //   rule.distributionIds,
        //   +rule.distributionRuleType === 1 ? callTeamList : callAgentList,
        // ),
        distributionIdsNameList:
          +rule.distributionRuleType === 1
            ? filterNameByAgentOrTeam(rule.distributionIds, callTeamList)
            : filterNameByAgent(rule.distributionIds, callAgentList),
        uniquesList:
          +rule.judgmentRuleType === 2
            ? rule.uniquesList.map(item => ({
              ...item,
              conditionValues: getConditionValues(item),
            }))
            : [],
      }))
    );
  };

  // 处理邮件和电话为输入框的情况
  const getConditionValues = item => {
    if (
      item.conditionType === 'customer_email' ||
      item.conditionType === 'customer_phone'
    ) {
      return item.conditionValues;
    } else {
      if (
        Array.isArray(item.conditionValues) &&
        item.conditionValues.length > 0
      ) {
        return item.conditionValues.join(',');
      }
    }
  };
  // 自定义验证函数：确保每条规则的 uniquesList 至少有一项有效
  const validateUniquesList = (_, value) => {
    const filteredRules = routingRule.filter(
      rule => +rule.judgmentRuleType === 2,
    );
    let flag = false;
    if (filteredRules?.length < 0) {
      flag = false;
    } else if (filteredRules?.length > 0) {
      const isValid =
        filteredRules &&
        filteredRules?.length > 0 &&
        filteredRules.every(rule =>
          rule.uniquesList.some(
            item => item.conditionType && item.conditionValues.length > 0,
          ),
        );
      if (!isValid) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'allocationRule.valid.item',
          }),
        });
        flag = true;
      } else {
        flag = false;
      }
    }
    return flag;
  };

  // 保存
  const handleResolved = () => {
    const newParams = {
      channelType: channelType,
      routingChannel: handleFormateChandle(),
      judgmentRuleType: determineTypeValue,
      routingRule: handleParamsId(routingRule),
    };
    if (history.location.state?.type !== 'add') {
      newParams.routingId = history.location.state.routingId;
    }
    if (history.location.state?.defaultRouting === 1) {
      const defaultParams = {
        judgmentRuleType: 3,
        routingRule: handleParamsId(routingRule),
        routingId: history.location.state.routingId,
        defaultRouting: history.location.state.defaultRouting,
      };
      delete defaultParams.routingRule[0].judgmentRuleType;
      delete defaultParams.routingRule[0].uniquesList;
      defaultForm.current?.validateFields().then(() => {
        setLoadingBtn(true);
        dispatch({
          type: 'allocation/saveRuleInfo',
          payload: defaultParams,
          callback: response => {
            if (response.code == 200) {
              setLoadingBtn(false);
              history.push('/allocationRule');
            } else {
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      });
    }
    if (validateUniquesList(newParams.routingRule)) return;
    formChannelType.current?.validateFields().then(() => {
      formRule.current?.validateFields().then(
        () => {
          setLoadingBtn(true);
          dispatch({
            type: 'allocation/saveRuleInfo',
            payload: newParams,
            callback: response => {
              setLoadingBtn(false);
              if (response.code == 200) {
                history.push('/allocationRule');
              } else {
                notification.error({
                  message: response.msg,
                });
              }
            },
          });
        },
        error => { },
      );
    });
  };
  // 取消
  const handleBack = () => {
    history.push('/allocationRule');
  };

  // 添加判断规则
  const handleAddNewRule = () => {
    // 生成6为随机ID
    const num = generateRandomNumber(6);
    let newObj = {
      onlyId: num,
      judgmentRuleType: 2, // 1:任意条件；2:个性化配置；3:默认规则
      distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定座席
      distributionIds: [], // 分配的目标id，团队就写团队id，座席就写座席
      stickiness: 1, // 粘性分配 0否 1是
      uniquesList: [
        {
          conditionType: '',
          conditionValues: '',
        },
      ],
    };
    setRoutingRule(prevRoutingRule => {
      const newRoutingRule = [...prevRoutingRule, newObj];
      return newRoutingRule;
    });
  };
  //删除规则
  const handleDelRoutingRule = index => {
    const newData = routingRule.filter((_, itemIndex) => itemIndex !== index);
    setRoutingRule(newData);
    setTimeout(() => {
      formRule.current?.setFieldsValue({ routingRule: newData });
    }, 0);
  };
  return (
    <Spin spinning={loading}>
      {/*  默认规则展示 */}
      {+history.location.state?.defaultRouting === 1 ? (
        routingRule &&
        routingRule?.length > 0 &&
        routingRule.map((ruleItem, ruleIndex) => {
          return (
            <div className={styles.defaultStyle}>
              <div className={styles.header}>
                <p className="blueBorder">
                  <FormattedMessage
                    id="allocation.intelligent.rules"
                    defaultMessage="智能分配规则"
                  />
                </p>
              </div>
              <div className={styles.selectWrapper}>
                <p className={styles.defaultTitle}>
                  <FormattedMessage
                    id="allocation.default.rules"
                    defaultMessage="默认规则"
                  />
                </p>
                <p className={styles.defaultTooip}>
                  <FormattedMessage
                    id="allocation.default.tooltip.rules"
                    defaultMessage="当系统没有适配到任何其他规则，都会路由到默认规则，请设置默认情况下工单分配的对象"
                  />
                </p>
                <div style={{ marginBottom: '10px' }}>
                  <Radio.Group
                    onChange={e =>
                      onChangeDistributionRuleType(e, ruleIndex, 'defaultForm')
                    }
                    value={+ruleItem.distributionRuleType}
                  >
                    <Radio value={1}>
                      <FormattedMessage
                        id="allocation.assign.to.specific.team"
                        defaultMessage="分配给特定团队"
                      />
                    </Radio>
                    <Radio value={2}>
                      <FormattedMessage
                        id="allocation.assign.to.specific.agent"
                        defaultMessage="分配给特定座席"
                      />
                    </Radio>
                  </Radio.Group>
                </div>
                <Form name="basic" ref={defaultForm} autoComplete="off">
                  {+ruleItem.distributionRuleType === 2 ? (
                    <Row>
                      <Col span={12}>
                        <Form.Item
                          label={
                            <FormattedMessage
                              id="allocation.assign.to.specific.agent"
                              defaultMessage="分配给特定座席"
                            />
                          }
                          name={`routingRule[${ruleIndex}].distributionIds`}
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="allocation.assign.to.specific.agent" />
                              ),
                            },
                          ]}
                        >
                          <Select
                            key={`${ruleIndex}-${ruleItem.distributionRuleType}`} // 强制更新
                            mode="multiple"
                            allowClear
                            style={{
                              width: '100%',
                              borderRadius: '6px',
                              color: '#3463FC',
                            }}
                            placeholder={getIntl().formatMessage({
                              id: 'allocation.assign.to.specific.agent',
                              defaultMessage: '分配给特定座席',
                            })}
                            onChange={value =>
                              handleChoseTeamOrAgent('agent', value, ruleIndex)
                            }
                            value={ruleItem.distributionIds}
                            options={callAgentList}
                            fieldNames={{
                              label: 'userName',
                              value: 'userId',
                              key: 'userId',
                            }}
                            filterOption={(inputValue, option) =>
                              option.userName
                                .toLowerCase()
                                .indexOf(inputValue.toLowerCase()) >= 0
                            }
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  ) : (
                    <Row>
                      <Col span={12}>
                        <Form.Item
                          name={`routingRule[${ruleIndex}].distributionIds`}
                          label={
                            <FormattedMessage
                              id="allocation.assign.to.specific.team"
                              defaultMessage="分配给特定团队"
                            />
                          }
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="allocation.assign.to.specific.team" />
                              ),
                            },
                          ]}
                        >
                          <Select
                            key={`${ruleIndex}-${ruleItem.distributionRuleType}`} // 强制更新
                            mode="multiple"
                            allowClear
                            style={{
                              width: '100%',
                              borderRadius: '6px',
                              color: '#3463FC',
                            }}
                            placeholder={getIntl().formatMessage({
                              id: 'allocation.assign.to.specific.team',
                              defaultMessage: '分配给特定团队',
                            })}
                            filterOption={(inputValue, option) =>
                              option.userName
                                .toLowerCase()
                                .indexOf(inputValue.toLowerCase()) >= 0
                            }
                            fieldNames={{
                              label: 'deptName',
                              value: 'deptId',
                              key: 'deptId',
                            }}
                            value={ruleItem.distributionIds}
                            onChange={value =>
                              handleChoseTeamOrAgent('team', value, ruleIndex)
                            }
                            options={callTeamList}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  )}
                </Form>
                <div>
                  <Checkbox
                    onChange={value => handleSetSticky(value, ruleIndex)}
                    checked={ruleItem.stickiness}
                  >
                    <span style={{ fontSize: '14px' }}>
                      <FormattedMessage
                        id="allocation.sticky.distribution"
                        defaultMessage="粘性分配"
                      />
                    </span>
                  </Checkbox>
                  <p
                    style={{
                      color: '#999',
                      fontSize: '12px',
                      marginTop: '8px',
                    }}
                  >
                    <FormattedMessage
                      id="allocation.when.the.system.assigns.tickets.it.will.try.to.route.them.to.the.agent.that.served.the.current.customer.last.time"
                      defaultMessage="系统进行工单分配时，会尽量路由到上次服务过当前客户的座席"
                    />
                  </p>
                </div>
              </div>
              <div className={styles.defaultFooterBtn}>
                {
                  <>
                    <Popconfirm
                      title={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.return.confirm',
                        defaultValue: '返回将清空表单，确定返回么？',
                      })}
                      onConfirm={handleBack}
                    >
                      <Button
                        style={{ marginRight: '20px' }}
                        className={styles.cancelHover}
                      >
                        <FormattedMessage
                          id="work.order.management.btn.cancel"
                          defaultMessage="取消"
                        />
                      </Button>
                    </Popconfirm>

                    <Button
                      loading={loadingBtn}
                      onClick={handleResolved}
                      type={'primary'}
                      className={styles.saveHover}
                    >
                      <FormattedMessage
                        id="work.order.management.btn.save"
                        defaultMessage="保存"
                      />
                    </Button>
                  </>
                }
              </div>
            </div>
          );
        })
      ) : (
        <div className={styles.wrapperContent}>
          <p className="blueBorder">
            {history.location.state?.type === 'add' ? (
              <FormattedMessage
                id="allocationRule.channel.add.rule"
                defaultMessage="新建智能分配规则"
              />
            ) : (
              <FormattedMessage
                id="allocationRule.channel.edit.rule"
                defaultMessage="修改智能分配规则"
              />
            )}
          </p>
          {/* 1.电话 2.聊天 3.邮件 */}
          <div className={styles.channelTypeWrapper}>
            <div className={`${styles.channelTypeItem}`}>
              <Radio.Group
                options={threeChannelList}
                block
                onChange={value => handleChangeThreeChannel(value)}
                value={currentThreeChannel}
              >
              </Radio.Group>
            </div>
          </div>
          {/* 选择当前适用渠道 */}
          <div className={styles.selectWrapper}>
            <p>
              <FormattedMessage
                id="allocationRule.channel.type"
                defaultMessage=" 请选择渠道类型"
              />
            </p>
            <Form
              name="basic"
              ref={formChannelType}
              autoComplete="off"
              initialValues={{ channelType: '0' }}
            >
              <Row>
                <Col>
                  <Form.Item
                    label={
                      <FormattedMessage
                        id="channel.type"
                        defaultMessage="渠道类型"
                      />
                    }
                    name="channelType"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage
                            id="allocationRule.channel.type"
                            defaultMessage="请选择渠道类型"
                          />
                        ),
                      },
                    ]}
                  >
                    <ChannelTypeSelect
                      value={channelType}
                      onChange={value => handleChangeSelect(value)}
                      channelTypeList={channelTypeList}
                      defaultStyle={{ width: '300px', color: '#3463FC' }}
                      popupClassName="selectFilterContent"
                    />
                  </Form.Item>
                </Col>
                <Col style={{ marginLeft: '20px' }}>
                  <Form.Item
                    label={
                      <FormattedMessage
                        id="contact.customers.title.other.information.source.channel"
                        defaultMessage="来源渠道"
                      />
                    }
                  // name="channelId"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      style={{
                        width: '300px',
                        borderRadius: 6,
                        color: '#3463FC',
                      }}
                      placeholder={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.sourceChannel.placeholder',
                        defaultMessage: '请选择来源渠道',
                      })}
                      fieldNames={{
                        label: 'name',
                        value: 'channelId',
                        key: 'channelId',
                      }}
                      filterOption={(inputValue, option) =>
                        option.name
                          .toLowerCase()
                          .indexOf(inputValue.toLowerCase()) >= 0
                      }
                      value={channelId}
                      options={channelOptions}
                      onChange={value => handleChangeSelectChannel(value)}
                    ></Select>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <p>
              <FormattedMessage
                id="allocationRule.channel.rules.type"
                defaultMessage=" 判断类型"
              />
            </p>
            <Row>
              <Col>
                <Radio.Group
                  onChange={onChangeCheck}
                  value={+determineTypeValue}
                >
                  <Radio value={1}>
                    <FormattedMessage
                      id="allocationRule.any.condition"
                      defaultMessage="任意条件"
                    />
                  </Radio>
                  <Radio value={2}>
                    <FormattedMessage
                      id="allocationRule.personalized.configuration"
                      defaultMessage="个性化配置"
                    />
                  </Radio>
                </Radio.Group>
              </Col>
            </Row>
          </div>
          <Form ref={formRule} name="basicTwo">
            {/*routingRulePersonalization*/}
            {routingRule &&
              routingRule?.length > 0 &&
              routingRule.map((ruleItem, ruleIndex) => {
                return (
                  <div
                    key={ruleItem.onlyId ? ruleItem.onlyId : ruleItem.routingId}
                  >
                    <div className={styles.rulesBox}>
                      <Row style={{ position: 'relative' }}>
                        {+determineTypeValue === 2 ? (
                          <p
                            style={{
                              color: '#333',
                              fontSize: '14px',
                              fontWeight: 700,
                            }}
                          >
                            {getIntl().formatMessage(
                              { id: 'allocationRule.judgment.rules' },
                              { number: +ruleIndex + 1 },
                            )}
                          </p>
                        ) : (
                          ''
                        )}
                        {/*删除规则 */}
                        {+ruleItem.judgmentRuleType === 2 &&
                          routingRule?.length > 1 && (
                            <Button
                              onClick={() => handleDelRoutingRule(ruleIndex)}
                              type="primary"
                              style={{
                                border: 'none',
                                padding: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: 'red',
                                position: 'absolute',
                                right: 0,
                              }}
                              danger
                              icon={<DeleteRed />}
                            >
                              <span style={{ marginLeft: 6 }}>
                                <FormattedMessage id="user.option.delete" />
                              </span>
                            </Button>
                          )}
                      </Row>
                      {/* 选择个性化配置 */}
                      {+determineTypeValue === 2 ? (
                        <div>
                          {ruleItem.uniquesList.map((item, index) => {
                            return (
                              <div
                                className={styles.personalityBox}
                                key={index}
                              >
                                <div className={styles.personalitySty}>
                                  <Form name="basic" autoComplete="off">
                                    <Row>
                                      <Col>
                                        <Form.Item
                                          label={
                                            <FormattedMessage
                                              id="allocation.personal.when"
                                              defaultMessage="当"
                                            />
                                          }
                                        >
                                          <Select
                                            optionLabelProp="label"
                                            optionFilterProp="children"
                                            showArrow={false}
                                            showSearch
                                            style={{
                                              // width: '100%',
                                              width: 300,
                                            }}
                                            filterOption={(
                                              inputValue,
                                              option,
                                            ) =>
                                              option.label
                                                .toLowerCase()
                                                .indexOf(
                                                  inputValue.toLowerCase(),
                                                ) >= 0
                                            }
                                            allowClear
                                            value={item.conditionType}
                                            onChange={value =>
                                              handleChangePersonaltyRule(
                                                value,
                                                ruleIndex,
                                                index,
                                              )
                                            }
                                          >
                                            {ruleList.map(group => (
                                              <OptGroup
                                                key={group.groupCode}
                                                label={group.groupName}
                                              >
                                                {group.routingRuleList.map(
                                                  option => (
                                                    <Option
                                                      key={option.code}
                                                      value={option.code}
                                                      label={option.name}
                                                    >
                                                      <div
                                                        style={{
                                                          display: 'flex',
                                                          alignItems: 'center',
                                                        }}
                                                      >
                                                        <div
                                                          style={{
                                                            width: '12px',
                                                            height: '12px',
                                                            backgroundColor:
                                                              option.tagColor,
                                                            marginRight: '4px',
                                                          }}
                                                        ></div>
                                                        <span>
                                                          {option.name}
                                                        </span>
                                                      </div>
                                                    </Option>
                                                  ),
                                                )}
                                              </OptGroup>
                                            ))}
                                          </Select>
                                        </Form.Item>
                                      </Col>

                                      <Col>
                                        <Form.Item
                                          label={
                                            <FormattedMessage
                                              id="allocation.personal.include"
                                              defaultMessage="包含"
                                            />
                                          }
                                        >
                                          {item.conditionType ===
                                            'ticket_type' ? (
                                            <Select
                                              mode="multiple"
                                              allowClear
                                              style={{
                                                // width: '100%',
                                                width: 400,
                                                color: '#3463FC',
                                              }}
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'create.work.order.work.order.type.required',
                                                  defaultValue:
                                                    '请选择工单类型',
                                                },
                                              )}
                                              onChange={value =>
                                                hanldeChangeChannalCode(
                                                  'workRecordType',
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                              value={item.conditionValues}
                                              options={workRecordTypeList}
                                              fieldNames={{
                                                label: 'workRecordTypeName',
                                                value: 'workRecordTypeId',
                                                key: 'workRecordTypeId',
                                              }}
                                              filterOption={(
                                                inputValue,
                                                option,
                                              ) =>
                                                option.workRecordTypeName
                                                  .toLowerCase()
                                                  .indexOf(
                                                    inputValue.toLowerCase(),
                                                  ) >= 0
                                              }
                                            />
                                          ) : item.conditionType ===
                                            'channel_name' ? (
                                            <Select
                                              mode="multiple"
                                              allowClear
                                              style={{
                                                // width: '100%',
                                                width: 400,
                                                color: '#3463FC',
                                              }}
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'customerInformation.add.basicInformation.channel.name',
                                                  defaultMessage:
                                                    '请选择渠道名称',
                                                },
                                              )}
                                              fieldNames={{
                                                label: 'name',
                                                value: 'channelId',
                                                key: 'channelId',
                                              }}
                                              value={item.conditionValues}
                                              onChange={value =>
                                                hanldeChangeChannalCode(
                                                  'channelId',
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                              filterOption={(
                                                inputValue,
                                                option,
                                              ) => {
                                                option.label
                                                  .toLowerCase()
                                                  .indexOf(
                                                    inputValue.toLowerCase(),
                                                  ) >= 0;
                                              }}
                                              options={channelOptions}
                                            />
                                          ) : item.conditionType ===
                                            'customer_label' ? (
                                            <div
                                              className={
                                                styles.customerTagContainer
                                              }
                                            >
                                              <Select
                                                tagRender={tagRender}
                                                optionLabelProp="label"
                                                optionFilterProp="children"
                                                showArrow={false}
                                                showSearch
                                                mode="multiple"
                                                style={{
                                                  // width: '100%',
                                                  width: 400,
                                                  color: '#3463FC',
                                                }}
                                                filterOption={(
                                                  inputValue,
                                                  option,
                                                ) =>
                                                  option.label
                                                    .toLowerCase()
                                                    .indexOf(
                                                      inputValue.toLowerCase(),
                                                    ) >= 0
                                                }
                                                allowClear
                                                placeholder={getIntl().formatMessage(
                                                  {
                                                    id:
                                                      'allocation.please.select.customer.tag',
                                                    defaultMessage:
                                                      '请选择客户标签',
                                                  },
                                                )}
                                                value={item.conditionValues}
                                                onClear={value =>
                                                  handleClearAllOption(
                                                    value,
                                                    index,
                                                    ruleIndex,
                                                  )
                                                }
                                                onDeselect={value =>
                                                  handleDeselect(
                                                    value,
                                                    index,
                                                    ruleIndex,
                                                  )
                                                }
                                                onSelect={value =>
                                                  hanldeChangeCustomerTag(
                                                    value,
                                                    index,
                                                    ruleIndex,
                                                  )
                                                }
                                              >
                                                {standardTagList.map(group => (
                                                  <OptGroup
                                                    key={group.categoryId}
                                                    label={
                                                      group.categoryContent !==
                                                        'private_tag_category_code'
                                                        ? group.categoryContent
                                                        : getIntl().formatMessage(
                                                          {
                                                            id:
                                                              'tag.management.tab.private',
                                                            defaultValue:
                                                              '私有标签',
                                                          },
                                                        )
                                                    }
                                                  >
                                                    {group.tagList.map(
                                                      option => (
                                                        <Option
                                                          key={option.tagId}
                                                          value={option.tagId}
                                                          label={
                                                            option.tagContent
                                                          }
                                                        >
                                                          <div
                                                            style={{
                                                              display: 'flex',
                                                              alignItems:
                                                                'center',
                                                            }}
                                                          >
                                                            <div
                                                              style={{
                                                                width: '12px',
                                                                height: '12px',
                                                                backgroundColor:
                                                                  option.tagColor,
                                                                marginRight:
                                                                  '4px',
                                                              }}
                                                            ></div>
                                                            <span>
                                                              {
                                                                option.tagContent
                                                              }
                                                            </span>
                                                          </div>
                                                        </Option>
                                                      ),
                                                    )}
                                                  </OptGroup>
                                                ))}
                                              </Select>
                                            </div>
                                          ) : item.conditionType ==
                                            'customer_preferred_language' ? (
                                            <Select
                                              mode="multiple"
                                              allowClear
                                              style={{
                                                width: 300,
                                                color: '#3463FC',
                                              }}
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'home.set.language.select',
                                                  defaultMessage: '请选择语言',
                                                },
                                              )}
                                              // fieldNames={{
                                              //   label: 'languageName',
                                              //   value: 'languageCode',
                                              //   key: 'languageCode',
                                              // }}
                                              value={item.conditionValues}
                                              onChange={value =>
                                                hanldeChangeLanguageCode(
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                              options={languageList.map(
                                                item => ({
                                                  label: item.label,
                                                  value: item.label,
                                                  key: item.value,
                                                }),
                                              )}
                                              filterOption={(
                                                inputValue,
                                                option,
                                              ) =>
                                                option.label
                                                  .toLowerCase()
                                                  .indexOf(
                                                    inputValue.toLowerCase(),
                                                  ) >= 0
                                              }
                                            // filterOption={(
                                            //   inputValue,
                                            //   option,
                                            // ) => {
                                            //   option.languageName
                                            //     .toLowerCase()
                                            //     .indexOf(
                                            //       inputValue.toLowerCase(),
                                            //     ) >= 0;
                                            // }}
                                            // options={languageList}
                                            />
                                          ) : item.conditionType ==
                                            'customer_level' ? (
                                            <Select
                                              mode="multiple"
                                              allowClear
                                              style={{
                                                width: '300px',
                                                borderRadius: 6,
                                                color: '#3463FC',
                                              }}
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'customerInformation.add.basicInformation.customerLevel.placeholder',
                                                  defaultMessage:
                                                    '请选择客户等级',
                                                },
                                              )}
                                              fieldNames={{
                                                label: 'gradeName',
                                                value: 'gradeId',
                                                key: 'gradeId',
                                              }}
                                              filterOption={(
                                                inputValue,
                                                option,
                                              ) =>
                                                option.gradeName
                                                  .toLowerCase()
                                                  .indexOf(
                                                    inputValue.toLowerCase(),
                                                  ) >= 0
                                              }
                                              value={item.conditionValues}
                                              options={gradeList}
                                              onChange={value =>
                                                hanldeChangeCustomerlevelCode(
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                            />
                                          ) : item.conditionType ==
                                            'customer_country' ? (
                                            <Select
                                              mode="multiple"
                                              allowClear
                                              style={{
                                                width: '300px',
                                                borderRadius: 6,
                                                color: '#3463FC',
                                              }}
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'new.customerDataGroupManagement.right.com3.placeholder.3',
                                                  defaultMessage: '请选择国家',
                                                },
                                              )}
                                              fieldNames={{
                                                label: 'countryName',
                                                value: 'countryCode',
                                                key: 'countryCode',
                                              }}
                                              filterOption={(
                                                inputValue,
                                                option,
                                              ) =>
                                                option.countryName
                                                  .toLowerCase()
                                                  .indexOf(
                                                    inputValue.toLowerCase(),
                                                  ) >= 0
                                              }
                                              value={item.conditionValues}
                                              options={customerCountry}
                                              onChange={value =>
                                                hanldeChangeCustomerCountryCode(
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                            />
                                          ) : item.conditionType ==
                                            'customer_email' ? (
                                            <Input
                                              value={item.conditionValues}
                                              allowClear
                                              style={{
                                                width: '300px',
                                                borderRadius: 6,
                                                color: '#3463FC',
                                                padding: '0px 11px',
                                                marginRight: '20px',
                                              }}
                                              onChange={value =>
                                                hanldeChangeCustomerEmailCode(
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'self.assessment.details.ask.customer.placeholder1',
                                                  defaultValue:
                                                    '请输入客户邮箱',
                                                },
                                              )}
                                            />
                                          ) : item.conditionType ==
                                            'customer_phone' ? (
                                            <Input
                                              value={item.conditionValues}
                                              allowClear
                                              style={{
                                                width: '300px',
                                                borderRadius: 6,
                                                color: '#3463FC',
                                                padding: '0px 11px',
                                                marginRight: '20px',
                                              }}
                                              className={styles.inputSty}
                                              onChange={value =>
                                                hanldeChangeCustomerPhoneCode(
                                                  value,
                                                  index,
                                                  ruleIndex,
                                                )
                                              }
                                              placeholder={getIntl().formatMessage(
                                                {
                                                  id:
                                                    'self.assessment.details.ask.customer.placeholder.phone',
                                                  defaultValue:
                                                    '请输入客户电话',
                                                },
                                              )}
                                            />
                                          ) : (
                                            <Select
                                              style={{
                                                // width: '100%',
                                                width: 400,
                                                color: '#3463FC',
                                              }}
                                            />
                                          )}
                                        </Form.Item>
                                      </Col>
                                      <Col>
                                        {/* 个性化配置删除 */}
                                        {index !== 0 && (
                                          <span
                                            style={{ marginLeft: '-12px' }}
                                            onClick={() =>
                                              handleDelUniqueRule(
                                                index,
                                                ruleIndex,
                                              )
                                            }
                                          >
                                            {Delete()}
                                          </span>
                                        )}
                                      </Col>
                                    </Row>
                                  </Form>
                                </div>
                                {ruleItem.uniquesList?.length < 10 &&
                                  index ===
                                  ruleItem.uniquesList?.length - 1 && (
                                    <div>
                                      <p className={styles.tootip}>
                                        <FormattedMessage
                                          id="allocation.personal.rules"
                                          defaultMessage="当您配置了多个条件时，这些条件是"
                                        />
                                        <span
                                          style={{
                                            color: '#3463FC',
                                            marginLeft: '2px',
                                          }}
                                        >
                                          <FormattedMessage
                                            id="allocation.more.rules.detail.and"
                                            defaultMessage="并且"
                                          />
                                        </span>
                                        <span style={{ marginLeft: '2px' }}>
                                          <FormattedMessage
                                            id="allocation.personal.relation"
                                            defaultMessage="的关系"
                                          />
                                        </span>
                                      </p>
                                      <Button
                                        onClick={() =>
                                          handleAddRoutingRule(ruleIndex)
                                        }
                                        icon={
                                          <AddRule
                                            style={{ fontSize: '16px' }}
                                          />
                                        }
                                        style={{
                                          borderRadius: '4px',
                                          display: 'flex',
                                          alignContent: 'center',
                                          alignItems: 'center',
                                          marginRight: '20px',
                                          marginBottom: '20px',
                                        }}
                                      >
                                        <FormattedMessage
                                          id="allocation.add.judgment.rules"
                                          defaultMessage="添加判断规则"
                                        />
                                      </Button>
                                    </div>
                                  )}
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        ''
                      )}
                      {/* 分配给团队和座席 */}
                      <Row>
                        <Col span={12}>
                          <p
                            style={{
                              color: '#333',
                              fontSize: '14px',
                              fontWeight: 700,
                            }}
                          >
                            <FormattedMessage
                              id="allocation.rules"
                              defaultMessage="分配规则"
                            />
                          </p>
                          <Form.Item>
                            <Radio.Group
                              onChange={e =>
                                onChangeDistributionRuleType(
                                  e,
                                  ruleIndex,
                                  'formRule',
                                )
                              }
                              value={+ruleItem.distributionRuleType}
                            >
                              <Radio value={1}>
                                <FormattedMessage
                                  id="allocation.assign.to.specific.team"
                                  defaultMessage="分配给特定团队"
                                />
                              </Radio>
                              <Radio value={2}>
                                <FormattedMessage
                                  id="allocation.assign.to.specific.agent"
                                  defaultMessage="分配给特定座席"
                                />
                              </Radio>
                            </Radio.Group>
                          </Form.Item>
                        </Col>
                      </Row>
                      {+ruleItem.distributionRuleType === 2 ? (
                        <Row>
                          <Col span={12}>
                            <Form.Item
                              label={
                                <FormattedMessage
                                  id="allocation.assign.to.specific.agent"
                                  defaultMessage="分配给特定座席"
                                />
                              }
                              // name={`routingRule[${ruleIndex}].distributionIds`}
                              name={[
                                'routingRule',
                                ruleIndex,
                                'distributionIds',
                              ]}
                              rules={[
                                {
                                  required: true,
                                  message: (
                                    <FormattedMessage id="allocation.assign.to.specific.agent" />
                                  ),
                                },
                              ]}
                            >
                              <Select
                                // key={`${ruleItem.onlyId}-${ruleItem.distributionRuleType}`} // 强制更新
                                mode="multiple"
                                allowClear
                                style={{
                                  width: '100%',
                                  borderRadius: '6px',
                                  color: '#3463FC',
                                }}
                                placeholder={getIntl().formatMessage({
                                  id: 'allocation.assign.to.specific.agent',
                                  defaultMessage: '分配给特定座席',
                                })}
                                onChange={value =>
                                  handleChoseTeamOrAgent(
                                    'agent',
                                    value,
                                    ruleIndex,
                                  )
                                }
                                value={ruleItem.distributionIds} // 确保绑定正确的值
                                options={callAgentList}
                                fieldNames={{
                                  label: 'userName',
                                  value: 'userId',
                                  key: 'userId',
                                }}
                                filterOption={(inputValue, option) =>
                                  option.userName
                                    .toLowerCase()
                                    .indexOf(inputValue.toLowerCase()) >= 0
                                }
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      ) : (
                        <Row>
                          <Col span={12}>
                            <Form.Item
                              // name={`routingRule[${ruleIndex}].distributionIds`}
                              name={[
                                'routingRule',
                                ruleIndex,
                                'distributionIds',
                              ]}
                              label={
                                <FormattedMessage
                                  id="allocation.assign.to.specific.team"
                                  defaultMessage="分配给特定团队"
                                />
                              }
                              rules={[
                                {
                                  required: true,
                                  message: (
                                    <FormattedMessage id="allocation.assign.to.specific.team" />
                                  ),
                                },
                              ]}
                            >
                              <Select
                                // key={`${ruleItem.onlyId}-${ruleItem.distributionRuleType}`} // 强制更新
                                mode="multiple"
                                allowClear
                                style={{
                                  width: '100%',
                                  borderRadius: '6px',
                                  color: '#3463FC',
                                }}
                                placeholder={getIntl().formatMessage({
                                  id: 'allocation.assign.to.specific.team',
                                  defaultMessage: '分配给特定团队',
                                })}
                                filterOption={(inputValue, option) =>
                                  option.deptName
                                    .toLowerCase()
                                    .indexOf(inputValue.toLowerCase()) >= 0
                                }
                                fieldNames={{
                                  label: 'deptName',
                                  value: 'deptId',
                                  key: 'deptId',
                                }}
                                value={ruleItem.distributionIds}
                                onChange={value =>
                                  handleChoseTeamOrAgent(
                                    'team',
                                    value,
                                    ruleIndex,
                                  )
                                }
                                options={callTeamList}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      )}
                      {/* 粘性分配 */}
                      <Row>
                        <Col>
                          <Form.Item>
                            <Checkbox
                              onChange={value =>
                                handleSetSticky(value, ruleIndex)
                              }
                              checked={+ruleItem.stickiness}
                            >
                              <span style={{ fontSize: '12px' }}>
                                <FormattedMessage
                                  id="allocation.sticky.distribution"
                                  defaultMessage="粘性分配"
                                />
                              </span>
                            </Checkbox>
                            <p
                              style={{
                                color: '#999',
                                fontSize: '12px',
                                marginTop: '8px',
                              }}
                            >
                              <FormattedMessage
                                id="allocation.when.the.system.assigns.tickets.it.will.try.to.route.them.to.the.agent.that.served.the.current.customer.last.time"
                                defaultMessage="系统进行工单分配时，会尽量路由到上次服务过当前客户的座席"
                              />
                            </p>
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>
                  </div>
                );
              })}
          </Form>
          <div>
            {routingRule?.length < 10 && +determineTypeValue === 2 && (
              <Button
                onClick={handleAddNewRule}
                type="primary"
                icon={<Add style={{ fontSize: '16px' }} />}
                style={{
                  borderRadius: '4px',
                  display: 'flex',
                  alignContent: 'center',
                  alignItems: 'center',
                  marginTop: '20px',
                }}
              >
                <FormattedMessage
                  id="allocation.add.rule"
                  defaultMessage="添加规则"
                />
              </Button>
            )}
          </div>
          <div className={styles.footerBtn}>
            {
              <>
                <Popconfirm
                  title={getIntl().formatMessage({
                    id:
                      'customerInformation.add.basicInformation.return.confirm',
                    defaultValue: '返回将清空表单，确定返回么？',
                  })}
                  onConfirm={handleBack}
                >
                  <Button
                    style={{ marginRight: '20px' }}
                    className={styles.cancelHover}
                  >
                    <FormattedMessage
                      id="work.order.management.btn.cancel"
                      defaultMessage="取消"
                    />
                  </Button>
                </Popconfirm>

                <Button
                  loading={loadingBtn}
                  onClick={handleResolved}
                  type={'primary'}
                  className={styles.saveHover}
                >
                  <FormattedMessage
                    id="work.order.management.btn.save"
                    defaultMessage="保存"
                  />
                </Button>
              </>
            }
          </div>
        </div>
      )}
    </Spin>
  );
};

export default AllocationRuleAdd;
